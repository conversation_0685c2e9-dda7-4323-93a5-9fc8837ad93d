package main

import (
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
)

var (
	archiveDir    string
	searchBaseDir string
	workerCount   int
	fileExt       string
	serverURL     string
	archiveStats  ArchiveStats
	mutex         sync.Mutex
)

type ArchiveStats struct {
	Total   int64
	Success int64
	Failed  int64
	Skipped int64
}

func main() {
	// Parse command-line arguments
	flag.StringVar(&searchBaseDir, "scan", ".", "Directory to scan for files")
	flag.StringVar(&archiveDir, "archive", "", "Directory to archive files")
	flag.StringVar(&fileExt, "ext", ".exe", "File extension to filter")
	flag.StringVar(&serverURL, "upload", "", "Server URL to upload files")
	flag.IntVar(&workerCount, "workers", 4, "Number of concurrent workers")
	flag.Parse()

	if archiveDir == "" && serverURL == "" {
		log.Fatal("Either archive or upload must be specified")
	}

	// Start scanning
	log.Printf("Starting scan in directory: %s", searchBaseDir)
	err := searchFiles(searchBaseDir)
	if err != nil {
		log.Fatalf("Error during file scan: %v", err)
	}

	log.Printf("Scan complete. Total: %d, Success: %d, Failed: %d, Skipped: %d",
		archiveStats.Total, archiveStats.Success, archiveStats.Failed, archiveStats.Skipped)
}

func searchFiles(searchDir string) error {
	fileQueue := make(chan string, 5000)
	var wg sync.WaitGroup

	// Start worker pool
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for filePath := range fileQueue {
				processFile(filePath)
			}
		}()
	}

	err := filepath.Walk(searchDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			log.Printf("Warning: Unable to access %s: %s", path, err)
			return nil
		}

		if !info.IsDir() && strings.HasSuffix(strings.ToLower(info.Name()), strings.ToLower(fileExt)) {
			fileQueue <- path
		}
		return nil
	})

	close(fileQueue)
	wg.Wait()
	return err
}

func processFile(filePath string) {
	atomic.AddInt64(&archiveStats.Total, 1)

	if archiveDir != "" {
		archiveStatus := archiveFile(filePath)
		log.Printf("Archived %s: %s", filePath, archiveStatus)
	}

	if serverURL != "" {
		uploadStatus := uploadFile(filePath)
		log.Printf("Uploaded %s: %s", filePath, uploadStatus)
	}
}

func archiveFile(sourcePath string) string {
	relativePath, err := filepath.Rel(searchBaseDir, sourcePath)
	if err != nil {
		relativePath = filepath.Base(sourcePath)
	}
	targetPath := filepath.Join(archiveDir, relativePath)
	targetDir := filepath.Dir(targetPath)

	if err := os.MkdirAll(targetDir, 0755); err != nil {
		atomic.AddInt64(&archiveStats.Failed, 1)
		return fmt.Sprintf("Failed to create directory: %v", err)
	}

	if err := copyFile(sourcePath, targetPath); err != nil {
		atomic.AddInt64(&archiveStats.Failed, 1)
		return fmt.Sprintf("Failed to copy file: %v", err)
	}

	atomic.AddInt64(&archiveStats.Success, 1)
	return "Success"
}

func uploadFile(filePath string) string {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Sprintf("Failed to open file: %v", err)
	}
	defer file.Close()

	resp, err := http.Post(serverURL, "application/octet-stream", file)
	if err != nil {
		return fmt.Sprintf("Failed to upload file: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Sprintf("Upload failed with status: %s", resp.Status)
	}

	return "Success"
}

func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()
	targetFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer targetFile.Close()

	_, err = io.Copy(targetFile, sourceFile)
	return err
}
