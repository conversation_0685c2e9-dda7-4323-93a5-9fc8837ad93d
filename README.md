# 分布式文件扫描系统

## 项目概述
分布式文件扫描系统是一个基于主从架构的远程文件扫描系统，旨在通过从端（Agent）收集文件并上传至主端（Server），由主端进行管理和安全扫描。该系统支持多平台运行，提供高效的文件扫描和安全管理能力。

## 技术栈
- **从端（Agent）**: Golang
- **主端（Server）**: Golang + Echo 框架
- **前端**: Vue.js + Tailwind CSS
- **依赖管理**: go mod

## 功能模块
### 从端（Agent）
1. **文件扫描**: 扫描指定目录下的文件。
2. **文件过滤**: 根据指定文件后缀过滤文件。
3. **文件打包**: 将过滤后的文件打包成压缩包。
4. **文件上传**: 将压缩包上传至主端服务器。
5. **多平台支持**: 支持 Windows x86_64、Linux x86_64、Linux ARM64。

### 主端（Server）
1. **SSH 远程部署**: 支持通过 SSH 将从端程序推送到目标系统。
2. **文件接收管理**: 接收从端上传的压缩包，按来源（IP/主机名）分类存储。
3. **安全扫描集成**: 调用第三方安全工具扫描上传的文件，解析扫描结果。
4. **结果展示**: Web 界面展示扫描结果和统计信息。
5. **设备管理**: 支持用户批量上传目标设备的 IP 地址和登录凭据，实现批量部署。

## 项目结构
```
ExeSentinel_Grid/
├── agent/               # 从端代码
│   ├── main.go          # 从端主程序入口
│   └── ...              # 其他从端相关代码
├── server/              # 主端代码
│   ├── backend/         # 后端代码
│   │   ├── main.go      # 主端后端入口
│   │   └── ...          # 其他后端相关代码
│   └── frontend/        # 前端代码
│       ├── src/         # Vue.js 源代码
│       └── ...          # 其他前端相关代码
├── docs/                # 文档
│   ├── README.md        # 项目说明
│   └── ...              # 其他文档
└── go.mod               # Go 依赖管理文件
```

## 开发计划
### 阶段 1: 项目初始化
- 创建项目目录结构。
- 编写 README.md 文件，描述项目架构和功能模块。
- 初始化 go mod。

### 阶段 2: 从端开发
- 实现从端的基本框架和占位符代码。
- 支持多平台编译。

### 阶段 3: 主端开发
- 搭建主端后端框架，集成 Echo。
- 搭建前端框架，集成 Vue.js 和 Tailwind CSS。

### 阶段 4: 功能实现
- 实现从端文件扫描、过滤、打包和上传功能。
- 实现主端文件接收、分类存储和安全扫描功能。
- 实现 SSH 远程部署和设备管理功能。

### 阶段 5: 测试与优化
- 测试从端和主端的功能。
- 优化系统性能和用户体验。

### 阶段 6: 部署与发布
- 编写部署文档。
- 发布系统。

## 部署指南
待补充。

